// audioUtils.ts - Refactored to use AudioStateManager

import { audioStateManager } from '../services/AudioStateManager';

/**
 * Play audio using the centralized AudioStateManager
 * This eliminates race conditions and global state issues
 */
export const playAudioWithFallback = async (
  audioUrl: string,
  onEnded?: () => void,
  onStarted?: () => void,
  onError?: (error: string) => void
): Promise<void> => {
  return audioStateManager.playAudio(audioUrl, onEnded, onStarted, onError);
};

/**
 * Check if audio is currently playing using AudioStateManager
 */
export const isAudioCurrentlyPlaying = (): boolean => {
  return audioStateManager.isPlaying();
};

/**
 * Stop current audio using AudioStateManager
 */
export const stopCurrentAudio = async (): Promise<void> => {
  await audioStateManager.stopAudio();
};

/**
 * Start background music with looping
 */
export const startBackgroundMusic = async (audioUrl: string): Promise<void> => {
  return audioStateManager.startBackgroundMusic(audioUrl);
};

/**
 * Stop background music
 */
export const stopBackgroundMusic = (): void => {
  audioStateManager.stopBackgroundMusic();
};

export const createAudioElement = (audioUrl: string, autoplay: boolean = true): HTMLAudioElement => {
  const audio = new Audio(audioUrl);
  audio.controls = true;
  audio.preload = 'auto';
  audio.volume = 0.8;

  if (autoplay) {
    // Intentar reproducir cuando esté listo
    audio.addEventListener('canplaythrough', () => {
      audio.play().catch(error => {
        console.warn('⚠️ Autoplay bloqueado:', error);
      });
    });
  }

  return audio;
};
