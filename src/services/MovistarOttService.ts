// services/MovistarOttService.ts
export class MovistarOttService {
    private baseUrl = "https://movistar-plus-agent-1081168799620.europe-west1.run.app";
    private token = "a2a-1f3822b882ff75942c3f0c31be1914778d63002e";

    constructor() {
        console.log("🎬 MovistarOttService initialized");
    }

    async init(): Promise<void> {
        console.log("🚀 Inicializando conexión con el agente...");
        const r = await fetch(`${this.baseUrl}/.well-known/agent.json`, {
            method: "GET",
            headers: { Authorization: `Bearer ${this.token}` },
        });
        if (!r.ok) throw new Error(`Agent not available: ${r.status}`);
        console.log("✅ Agente disponible:", await r.json());
    }

    // =========== Opción A: FINAL ONLY (bloqueante, simple) ===========
    async sendToAgentFinalOnly(message: string): Promise<string> {
        const body = {
            jsonrpc: "2.0",
            id: `task_${Date.now()}`,
            method: "message/send",
            params: {
                message: {
                    kind: "message",
                    messageId: `msg_${Date.now()}`,
                    role: "user",
                    parts: [{ kind: "text", text: message }],
                },
                configuration: {
                    blocking: true,
                    acceptedOutputModes: ["text/plain", "json"],
                },
            },
        };

        const res = await fetch(`${this.baseUrl}/`, {
            method: "POST",
            headers: {
                Authorization: `Bearer ${this.token}`,
                "Content-Type": "application/json",
                Accept: "application/json",
            },
            body: JSON.stringify(body),
        });

        if (!res.ok) throw new Error(`HTTP ${res.status}: ${res.statusText}`);
        const json = await res.json();

        // Extrae texto del result
        const result = json?.result;
        if (!result) return "Respuesta sin result";
        if (result.kind === "message" && Array.isArray(result.parts)) {
            const text = result.parts.filter((p: any) => p?.kind === "text").map((p: any) => p.text).join("");
            return text || "(vacío)";
        }
        // Fallbacks
        if (result.content) return result.content;
        if (result.response) return result.response;
        if (result.message) return result.message;
        if (result.output) return result.output;

        return JSON.stringify(result);
    }

    async close(): Promise<void> {
        console.log("🔒 Cerrando conexión con el agente");
    }
}
