/**
 * Centralized Audio State Manager
 * Eliminates race conditions and global state issues
 * Now supports background music with ducking for narration
 */

export type AudioState = 'idle' | 'loading' | 'playing' | 'ended' | 'error';
export type AudioType = 'narration' | 'background';

export interface AudioStateData {
  state: AudioState;
  currentAudio: HTMLAudioElement | null;
  duration: number;
  currentTime: number;
  error: string | null;
  backgroundMusic: HTMLAudioElement | null;
  isBackgroundMusicPlaying: boolean;
}

export type AudioStateListener = (state: AudioStateData) => void;

class AudioStateManager {
  private state: AudioState = 'idle';
  private currentAudio: HTMLAudioElement | null = null;
  private listeners: Set<AudioStateListener> = new Set();
  private timeoutId: NodeJS.Timeout | null = null;
  private callbackExecuted = false;

  // Background music support
  private backgroundMusic: HTMLAudioElement | null = null;
  private isBackgroundMusicPlaying = false;
  private originalBackgroundVolume = 0.3; // Default background volume
  private duckedBackgroundVolume = 0.1; // Reduced volume during narration
  private volumeTransitionDuration = 500; // ms for smooth volume transitions

  // Callbacks
  private onEndedCallback?: () => void;
  private onStartedCallback?: () => void;
  private onErrorCallback?: (error: string) => void;

  /**
   * Get current audio state data
   */
  getState(): AudioStateData {
    return {
      state: this.state,
      currentAudio: this.currentAudio,
      duration: this.currentAudio?.duration || 0,
      currentTime: this.currentAudio?.currentTime || 0,
      error: null,
      backgroundMusic: this.backgroundMusic,
      isBackgroundMusicPlaying: this.isBackgroundMusicPlaying
    };
  }

  /**
   * Check if audio is currently playing
   */
  isPlaying(): boolean {
    if (!this.currentAudio) return false;

    return !this.currentAudio.paused &&
           !this.currentAudio.ended &&
           this.currentAudio.readyState > 2 &&
           this.currentAudio.currentTime > 0;
  }

  /**
   * Subscribe to state changes
   */
  subscribe(listener: AudioStateListener): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * Notify all listeners of state change
   */
  private notifyListeners(): void {
    const stateData = this.getState();
    this.listeners.forEach(listener => listener(stateData));
  }

  /**
   * Update state and notify listeners
   */
  private setState(newState: AudioState): void {
    if (this.state !== newState) {
      console.log(`🔊 [AudioManager] State: ${this.state} → ${newState}`);
      this.state = newState;
      this.notifyListeners();
    }
  }

  /**
   * Execute callback only once
   */
  private executeCallback(source: string): void {
    if (!this.callbackExecuted && this.onEndedCallback) {
      this.callbackExecuted = true;
      console.log(`🎤 [AudioManager] Callback executed from: ${source}`);
      this.clearTimeout();
      this.onEndedCallback();
    }
  }

  /**
   * Clear any pending timeout
   */
  private clearTimeout(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  /**
   * Smoothly transition volume over time
   */
  private smoothVolumeTransition(
    audio: HTMLAudioElement,
    targetVolume: number,
    duration: number = this.volumeTransitionDuration
  ): Promise<void> {
    return new Promise((resolve) => {
      const startVolume = audio.volume;
      const volumeDiff = targetVolume - startVolume;
      const steps = 20; // Number of volume steps
      const stepDuration = duration / steps;
      const stepSize = volumeDiff / steps;

      let currentStep = 0;

      const interval = setInterval(() => {
        currentStep++;
        const newVolume = startVolume + (stepSize * currentStep);

        if (currentStep >= steps) {
          audio.volume = targetVolume;
          clearInterval(interval);
          resolve();
        } else {
          audio.volume = Math.max(0, Math.min(1, newVolume));
        }
      }, stepDuration);
    });
  }

  /**
   * Duck background music volume down
   */
  private async duckBackgroundMusic(): Promise<void> {
    if (this.backgroundMusic && this.isBackgroundMusicPlaying) {
      console.log('🔇 [AudioManager] Ducking background music');
      await this.smoothVolumeTransition(this.backgroundMusic, this.duckedBackgroundVolume);
    }
  }

  /**
   * Restore background music volume
   */
  private async restoreBackgroundMusic(): Promise<void> {
    if (this.backgroundMusic && this.isBackgroundMusicPlaying) {
      console.log('🔊 [AudioManager] Restoring background music volume');
      await this.smoothVolumeTransition(this.backgroundMusic, this.originalBackgroundVolume);
    }
  }

  /**
   * Start background music
   */
  async startBackgroundMusic(audioUrl: string): Promise<void> {
    try {
      // Stop any existing background music
      this.stopBackgroundMusic();

      console.log('🎵 [AudioManager] Starting background music');

      const audio = new Audio(audioUrl);
      audio.preload = 'auto';
      audio.volume = this.originalBackgroundVolume;
      audio.loop = true; // Background music should loop

      this.backgroundMusic = audio;

      // Setup background music event listeners
      audio.addEventListener('playing', () => {
        console.log('🎵 [AudioManager] Background music started playing');
        this.isBackgroundMusicPlaying = true;
        this.notifyListeners();
      });

      audio.addEventListener('ended', () => {
        console.log('🎵 [AudioManager] Background music ended');
        this.isBackgroundMusicPlaying = false;
        this.notifyListeners();
      });

      audio.addEventListener('error', (e) => {
        console.error('❌ [AudioManager] Background music error:', e);
        this.isBackgroundMusicPlaying = false;
        this.notifyListeners();
      });

      await audio.play();
      console.log('✅ [AudioManager] Background music play initiated');

    } catch (error) {
      console.warn('⚠️ [AudioManager] Error starting background music:', error);
      this.isBackgroundMusicPlaying = false;
      this.notifyListeners();
    }
  }

  /**
   * Stop background music
   */
  stopBackgroundMusic(): void {
    if (this.backgroundMusic) {
      this.backgroundMusic.pause();
      this.backgroundMusic.currentTime = 0;
      this.backgroundMusic = null;
      this.isBackgroundMusicPlaying = false;
      console.log('🛑 [AudioManager] Background music stopped');
      this.notifyListeners();
    }
  }

  /**
   * Setup audio event listeners
   */
  private setupAudioListeners(audio: HTMLAudioElement): void {
    // Playing event - audio actually started
    audio.addEventListener('playing', async () => {
      console.log('🔊 [AudioManager] Audio started playing');
      this.setState('playing');

      // Duck background music when narration starts
      await this.duckBackgroundMusic();

      if (this.onStartedCallback) {
        this.onStartedCallback();
      }
    });

    // Ended event - audio finished naturally
    audio.addEventListener('ended', async () => {
      console.log('🔊 [AudioManager] Audio ended naturally');
      this.setState('ended');

      // Restore background music volume when narration ends
      await this.restoreBackgroundMusic();

      this.executeCallback('ended-event');
    });

    // Error event
    audio.addEventListener('error', (e) => {
      console.error('❌ [AudioManager] Audio error:', e);
      this.setState('error');
      this.executeCallback('error-fallback');
      if (this.onErrorCallback) {
        this.onErrorCallback(`Audio error: ${e.type}`);
      }
    });

    // Metadata loaded - setup safety timeout
    audio.addEventListener('loadedmetadata', () => {
      const duration = audio.duration || 10;
      const timeoutDuration = (duration + 3) * 1000;

      console.log(`⏱️ [AudioManager] Setting timeout: ${timeoutDuration}ms for ${duration}s audio`);

      this.timeoutId = setTimeout(() => {
        if (!this.callbackExecuted && this.state === 'playing') {
          console.log('⏰ [AudioManager] Safety timeout reached');
          this.executeCallback('timeout-fallback');
        }
      }, timeoutDuration);
    });
  }

  /**
   * Play audio with proper state management
   */
  async playAudio(
    audioUrl: string,
    onEnded?: () => void,
    onStarted?: () => void,
    onError?: (error: string) => void
  ): Promise<void> {
    // Stop any current audio
    await this.stopAudio();

    // Reset state
    this.callbackExecuted = false;
    this.onEndedCallback = onEnded;
    this.onStartedCallback = onStarted;
    this.onErrorCallback = onError;

    try {
      this.setState('loading');

      const audio = new Audio(audioUrl);
      audio.preload = 'auto';
      audio.volume = 0.8;

      this.currentAudio = audio;
      this.setupAudioListeners(audio);

      await audio.play();
      console.log('✅ [AudioManager] Audio play initiated');

    } catch (error) {
      console.warn('⚠️ [AudioManager] Error starting playback:', error);
      this.setState('error');

      // Handle AbortError specially
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('⏳ [AudioManager] AbortError - waiting to see if audio recovers...');

        // Give audio time to recover, then check if it actually started
        setTimeout(() => {
          if (this.state !== 'playing' && !this.callbackExecuted) {
            console.log('⏰ [AudioManager] AbortError confirmed - audio never started');
            this.executeCallback('abort-confirmed');
          }
        }, 3000);
      } else {
        // For other errors, execute callback after short delay
        setTimeout(() => {
          if (!this.callbackExecuted) {
            this.executeCallback('error-timeout-fallback');
          }
        }, 1000);

        if (this.onErrorCallback) {
          this.onErrorCallback(`Playback error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    }
  }

  /**
   * Stop current audio
   */
  async stopAudio(): Promise<void> {
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
      this.currentAudio = null;
      console.log('🛑 [AudioManager] Audio stopped');

      // Restore background music volume when narration is stopped
      await this.restoreBackgroundMusic();
    }

    this.clearTimeout();
    this.callbackExecuted = true; // Prevent any pending callbacks
    this.setState('idle');
  }

  /**
   * Cleanup resources
   */
  async destroy(): Promise<void> {
    await this.stopAudio();
    this.stopBackgroundMusic();
    this.listeners.clear();
  }
}

// Singleton instance
export const audioStateManager = new AudioStateManager();
