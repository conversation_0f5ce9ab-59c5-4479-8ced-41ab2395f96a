import React from "react";

interface RulesPopupProps {
  isOpen: boolean;
  onClose: () => void;
}

/**
 * Rules Popup Component
 * 
 * Displays the game rules in a modal popup.
 * Features:
 * - Clear explanation of game mechanics
 * - Step-by-step instructions
 * - Tips for better gameplay
 * - Modal overlay design
 */
export const RulesPopup: React.FC<RulesPopupProps> = ({ isOpen, onClose }) => {
  if (!isOpen) {
    return null;
  }

  return (
    <>
      {/* Overlay */}
      <div
        style={{
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: "rgba(0, 0, 0, 0.6)",
          zIndex: 1000,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          padding: "20px",
        }}
        onClick={onClose}
      >
        {/* Modal Content */}
        <div
          style={{
            backgroundColor: "white",
            borderRadius: "16px",
            padding: "32px",
            maxWidth: "600px",
            width: "100%",
            maxHeight: "80vh",
            overflowY: "auto",
            boxShadow: "0 20px 40px rgba(0, 0, 0, 0.3)",
            position: "relative",
            animation: "modalSlideIn 0.3s ease-out"
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Close Button */}
          <button
            onClick={onClose}
            style={{
              position: "absolute",
              top: "16px",
              right: "16px",
              backgroundColor: "transparent",
              border: "none",
              fontSize: "24px",
              cursor: "pointer",
              color: "#666",
              width: "32px",
              height: "32px",
              borderRadius: "50%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              transition: "all 0.2s ease"
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = "#f0f0f0";
              e.currentTarget.style.color = "#333";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = "transparent";
              e.currentTarget.style.color = "#666";
            }}
          >
            ×
          </button>

          {/* Title */}
          <div style={{ textAlign: "center", marginBottom: "24px" }}>
            <div style={{ fontSize: "48px", marginBottom: "8px" }}>📋</div>
            <h2 style={{
              color: "#333",
              margin: 0,
              fontSize: "28px",
              fontWeight: "600"
            }}>
              Reglas del Juego
            </h2>
          </div>

          {/* Game Objective */}
          <div style={{ marginBottom: "24px" }}>
            <h3 style={{
              color: "#007bff",
              fontSize: "20px",
              fontWeight: "600",
              marginBottom: "12px",
              display: "flex",
              alignItems: "center",
              gap: "8px"
            }}>
              🎯 Objetivo
            </h3>
            <p style={{
              color: "#666",
              fontSize: "16px",
              lineHeight: "1.6",
              margin: 0
            }}>
              Adivina el personaje misterioso haciendo preguntas inteligentes. 
              Tienes un número limitado de intentos, ¡así que piensa bien cada pregunta!
            </p>
          </div>

          {/* How to Play */}
          <div style={{ marginBottom: "24px" }}>
            <h3 style={{
              color: "#007bff",
              fontSize: "20px",
              fontWeight: "600",
              marginBottom: "12px",
              display: "flex",
              alignItems: "center",
              gap: "8px"
            }}>
              🎮 Cómo Jugar
            </h3>
            <ol style={{
              color: "#666",
              fontSize: "16px",
              lineHeight: "1.6",
              paddingLeft: "20px"
            }}>
              <li style={{ marginBottom: "8px" }}>
                <strong>Habla con el micrófono:</strong> Haz clic en el botón del micrófono y haz tu pregunta en voz alta
              </li>
              <li style={{ marginBottom: "8px" }}>
                <strong>Escucha la respuesta:</strong> La IA responderá con pistas sobre el personaje
              </li>
              <li style={{ marginBottom: "8px" }}>
                <strong>Usa las pistas:</strong> Cada respuesta válida te dará una pista que se guardará
              </li>
              <li style={{ marginBottom: "8px" }}>
                <strong>Adivina el personaje:</strong> Cuando creas saber quién es, di su nombre completo
              </li>
            </ol>
          </div>

          {/* Game Features */}
          <div style={{ marginBottom: "24px" }}>
            <h3 style={{
              color: "#007bff",
              fontSize: "20px",
              fontWeight: "600",
              marginBottom: "12px",
              display: "flex",
              alignItems: "center",
              gap: "8px"
            }}>
              ⚡ Funciones del Juego
            </h3>
            <div style={{ display: "grid", gap: "12px" }}>
              <div style={{
                backgroundColor: "#f8f9fa",
                padding: "12px",
                borderRadius: "8px",
                border: "1px solid #e9ecef"
              }}>
                <strong style={{ color: "#333" }}>💡 Pistas:</strong>
                <span style={{ color: "#666", marginLeft: "8px" }}>
                  Consulta todas las pistas obtenidas hasta el momento
                </span>
              </div>
              <div style={{
                backgroundColor: "#f8f9fa",
                padding: "12px",
                borderRadius: "8px",
                border: "1px solid #e9ecef"
              }}>
                <strong style={{ color: "#333" }}>❤️ Vidas:</strong>
                <span style={{ color: "#666", marginLeft: "8px" }}>
                  Tienes un número limitado de intentos para adivinar
                </span>
              </div>
              <div style={{
                backgroundColor: "#f8f9fa",
                padding: "12px",
                borderRadius: "8px",
                border: "1px solid #e9ecef"
              }}>
                <strong style={{ color: "#333" }}>🚪 Salir:</strong>
                <span style={{ color: "#666", marginLeft: "8px" }}>
                  Reinicia el juego en cualquier momento
                </span>
              </div>
            </div>
          </div>

          {/* Tips */}
          <div style={{ marginBottom: "24px" }}>
            <h3 style={{
              color: "#28a745",
              fontSize: "20px",
              fontWeight: "600",
              marginBottom: "12px",
              display: "flex",
              alignItems: "center",
              gap: "8px"
            }}>
              💭 Consejos
            </h3>
            <ul style={{
              color: "#666",
              fontSize: "16px",
              lineHeight: "1.6",
              paddingLeft: "20px"
            }}>
              <li style={{ marginBottom: "8px" }}>
                Haz preguntas específicas sobre profesión, nacionalidad, época, etc.
              </li>
              <li style={{ marginBottom: "8px" }}>
                Evita preguntas muy generales como "¿es famoso?"
              </li>
              <li style={{ marginBottom: "8px" }}>
                Usa las pistas anteriores para hacer preguntas más precisas
              </li>
              <li style={{ marginBottom: "8px" }}>
                Habla claro y espera a que termine la respuesta antes de continuar
              </li>
            </ul>
          </div>

          {/* Start Button */}
          <div style={{ textAlign: "center" }}>
            <button
              onClick={onClose}
              style={{
                backgroundColor: "#007bff",
                color: "white",
                border: "none",
                borderRadius: "8px",
                padding: "12px 32px",
                fontSize: "16px",
                fontWeight: "600",
                cursor: "pointer",
                transition: "all 0.2s ease"
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = "#0056b3";
                e.currentTarget.style.transform = "translateY(-1px)";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "#007bff";
                e.currentTarget.style.transform = "translateY(0)";
              }}
            >
              ¡Entendido, vamos a jugar! 🚀
            </button>
          </div>
        </div>
      </div>

      {/* CSS Animation */}
      <style>{`
        @keyframes modalSlideIn {
          from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }
      `}</style>
    </>
  );
};
