import React from "react";

interface AnimatedVoiceButtonProps {
  state: string;
  isActive: boolean;
  onClick: () => void;
  disabled?: boolean;
}

/**
 * Animated voice button component that changes appearance based on conversation state
 *
 * Features:
 * - Dynamic styling based on conversation state (listening, processing, speaking)
 * - CSS animations for visual feedback
 * - Hover effects for better UX
 */
export const AnimatedVoiceButton: React.FC<AnimatedVoiceButtonProps> = ({
  state,
  isActive,
  onClick,
  disabled = false
}) => {
  /**
   * Get button configuration based on current state
   * Returns icon, text, colors, and animation properties
   */
  const getButtonConfig = () => {
    // Handle disabled state first
    if (disabled) {
      return {
        icon: "🔇",
        text: "Juego Finalizado",
        color: "#6c757d",
        bgColor: "#f8f9fa",
        borderColor: "#dee2e6",
      };
    }

    if (!isActive) {
      return {
        icon: "🎤",
        text: "Iniciar Conversación",
        color: "#28a745",
        bgColor: "#d4edda",
        borderColor: "#c3e6cb",
      };
    }

    switch (state) {
      case "listening":
        return {
          icon: "🎤",
          text: "Escuchando...",
          color: "#28a745",
          bgColor: "#d4edda",
          borderColor: "#c3e6cb",
          pulse: true,
        };
      case "processing":
        return {
          icon: "⚙️",
          text: "Procesando...",
          color: "#ffc107",
          bgColor: "#fff3cd",
          borderColor: "#ffeaa7",
          spin: true,
        };
      case "speaking":
        return {
          icon: "🔊",
          text: "IA hablando...",
          color: "#17a2b8",
          bgColor: "#d1ecf1",
          borderColor: "#bee5eb",
          bounce: true,
        };
      default:
        return {
          icon: "⭕",
          text: "Activo",
          color: "#6c757d",
          bgColor: "#e2e3e5",
          borderColor: "#d6d8db",
        };
    }
  };

  const config = getButtonConfig();

  // Dynamic animation styles based on state
  const animationStyle = {
    ...(config.pulse && { animation: "pulse 1.5s ease-in-out infinite" }),
    ...(config.spin && { animation: "spin 1s linear infinite" }),
    ...(config.bounce && { animation: "bounce 0.6s ease-in-out infinite" }),
  };

  return (
    <>
      {/* CSS animations */}
      <style>{`
        @keyframes pulse {
          0%, 100% { transform: scale(1); opacity: 1; }
          50% { transform: scale(1.05); opacity: 0.8; }
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
          40% { transform: translateY(-5px); }
          60% { transform: translateY(-3px); }
        }
      `}</style>

      <button
        onClick={disabled ? undefined : onClick}
        disabled={disabled}
        style={{
          display: "flex",
          alignItems: "center",
          gap: "12px",
          padding: "16px 24px",
          backgroundColor: config.bgColor,
          color: config.color,
          border: `2px solid ${config.borderColor}`,
          borderRadius: "50px",
          cursor: disabled ? "not-allowed" : "pointer",
          fontSize: "16px",
          fontWeight: "bold",
          boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
          transition: "all 0.3s ease",
          opacity: disabled ? 0.6 : 1,
          ...(!disabled && animationStyle),
        }}
        onMouseOver={disabled ? undefined : (e) => {
          e.currentTarget.style.transform = "translateY(-2px)";
          e.currentTarget.style.boxShadow = "0 6px 20px rgba(0,0,0,0.15)";
        }}
        onMouseOut={disabled ? undefined : (e) => {
          e.currentTarget.style.transform = "translateY(0)";
          e.currentTarget.style.boxShadow = "0 4px 12px rgba(0,0,0,0.1)";
        }}
      >
        <span style={{ fontSize: "20px" }}>{config.icon}</span>
        <span>{config.text}</span>
      </button>
    </>
  );
};
