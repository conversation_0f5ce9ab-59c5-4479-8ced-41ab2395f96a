/**
 * Test runner for timing and race condition fixes
 * Run this to verify all timing issues have been resolved
 */

import { audioStateManager } from '../services/AudioStateManager';
import { conversationStateManager } from '../services/ConversationStateManager';
import { conversationStateMachine } from '../services/ConversationStateMachine';

interface TestResult {
  name: string;
  passed: boolean;
  error?: string;
  duration: number;
}

class TimingTestRunner {
  private results: TestResult[] = [];

  async runAllTests(): Promise<void> {
    console.log('🧪 Starting timing and race condition tests...\n');

    await this.runTest('Audio State Manager - Basic Flow', this.testAudioStateManagerBasicFlow);
    await this.runTest('Audio State Manager - Race Conditions', this.testAudioRaceConditions);
    await this.runTest('Conversation State Machine - Valid Transitions', this.testStateMachineTransitions);
    await this.runTest('Conversation State Machine - Concurrent Access', this.testStateMachineConcurrency);
    await this.runTest('Conversation State Manager - Auto Activation', this.testAutoActivation);
    await this.runTest('Integration - Complete Flow', this.testCompleteFlow);
    await this.runTest('Error Recovery', this.testErrorRecovery);

    this.printResults();
  }

  private async runTest(name: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now();

    try {
      await testFn.call(this);
      const duration = Date.now() - startTime;
      this.results.push({ name, passed: true, duration });
      console.log(`✅ ${name} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        passed: false,
        error: error instanceof Error ? error.message : String(error),
        duration
      });
      console.log(`❌ ${name} (${duration}ms): ${error}`);
    }

    // Reset state between tests
    await audioStateManager.stopAudio();
    conversationStateManager.reset();
    conversationStateMachine.reset();
  }

  private async testAudioStateManagerBasicFlow(): Promise<void> {
    let callbackExecuted = false;
    let startedCallbackExecuted = false;

    // Mock audio element
    const mockAudio = {
      paused: true,
      ended: false,
      readyState: 0,
      currentTime: 0,
      duration: 5,
      volume: 1,
      preload: 'none',
      listeners: {} as any,
      addEventListener(event: string, callback: Function) {
        if (!this.listeners[event]) this.listeners[event] = [];
        this.listeners[event].push(callback);
      },
      async play() {
        this.paused = false;
        this.readyState = 4;
        setTimeout(() => this.listeners.loadedmetadata?.forEach((cb: Function) => cb()), 5);
        setTimeout(() => this.listeners.playing?.forEach((cb: Function) => cb()), 10);
      },
      simulateEnd() {
        this.ended = true;
        this.listeners.ended?.forEach((cb: Function) => cb());
      }
    };

    (global as any).Audio = jest.fn().mockImplementation(() => mockAudio);

    await audioStateManager.playAudio(
      'test.mp3',
      () => { callbackExecuted = true; },
      () => { startedCallbackExecuted = true; }
    );

    await new Promise(resolve => setTimeout(resolve, 15));

    if (!startedCallbackExecuted) {
      throw new Error('Started callback not executed');
    }

    mockAudio.simulateEnd();
    await new Promise(resolve => setTimeout(resolve, 10));

    if (!callbackExecuted) {
      throw new Error('End callback not executed');
    }

    if (audioStateManager.getState().state !== 'ended') {
      throw new Error('Audio state not updated to ended');
    }
  }

  private async testAudioRaceConditions(): Promise<void> {
    let callbackCount = 0;

    // Start multiple audio plays rapidly
    const promises = Array.from({ length: 5 }, (_, i) =>
      audioStateManager.playAudio(`test-${i}.mp3`, () => callbackCount++)
    );

    await Promise.all(promises);

    // Should prevent multiple callbacks
    if (callbackCount > 1) {
      throw new Error(`Expected at most 1 callback, got ${callbackCount}`);
    }
  }

  private async testStateMachineTransitions(): Promise<void> {
    // Test valid transition sequence
    let success = await conversationStateMachine.transitionTo('listening');
    if (!success) throw new Error('Failed to transition to listening');

    success = await conversationStateMachine.transitionTo('processing');
    if (!success) throw new Error('Failed to transition to processing');

    success = await conversationStateMachine.transitionTo('speaking');
    if (!success) throw new Error('Failed to transition to speaking');

    success = await conversationStateMachine.transitionTo('idle');
    if (!success) throw new Error('Failed to transition to idle');

    if (conversationStateMachine.getCurrentState() !== 'idle') {
      throw new Error('Final state is not idle');
    }
  }

  private async testStateMachineConcurrency(): Promise<void> {
    // Start multiple transitions simultaneously
    const promises = [
      conversationStateMachine.transitionTo('listening'),
      conversationStateMachine.transitionTo('speaking'),
      conversationStateMachine.transitionTo('processing')
    ];

    const results = await Promise.all(promises);
    const successCount = results.filter(Boolean).length;

    if (successCount !== 1) {
      throw new Error(`Expected exactly 1 successful transition, got ${successCount}`);
    }
  }

  private async testAutoActivation(): Promise<void> {
    let activationCount = 0;

    conversationStateManager.setCanAutoActivate(true);
    conversationStateManager.setActivationHandlers(
      async () => {
        activationCount++;
        return true;
      },
      () => {}
    );

    // Simulate conditions for auto-activation
    conversationStateManager.setConversationState('speaking');
    conversationStateManager.setIsAudioPlaying(true);

    await new Promise(resolve => setTimeout(resolve, 10));

    conversationStateManager.setConversationState('idle');
    conversationStateManager.setIsAudioPlaying(false);

    // Wait for auto-activation logic
    await new Promise(resolve => setTimeout(resolve, 1100));

    if (activationCount === 0) {
      throw new Error('Auto-activation did not trigger');
    }

    if (activationCount > 1) {
      throw new Error(`Auto-activation triggered ${activationCount} times, expected 1`);
    }
  }

  private async testCompleteFlow(): Promise<void> {
    const events: string[] = [];
    let audioFinished = false;

    // Set up state tracking
    audioStateManager.subscribe((state) => {
      events.push(`audio:${state.state}`);
    });

    conversationStateManager.subscribe((state) => {
      events.push(`conversation:${state.state}`);
    });

    // Simulate complete conversation flow
    await conversationStateMachine.transitionTo('listening');
    await conversationStateMachine.transitionTo('processing');
    await conversationStateMachine.transitionTo('speaking');

    // Start audio playback
    await audioStateManager.playAudio(
      'response.mp3',
      () => { audioFinished = true; }
    );

    await new Promise(resolve => setTimeout(resolve, 20));

    // Simulate audio ending
    const mockAudio = audioStateManager.getState().currentAudio as any;
    mockAudio?.simulateEnd?.();

    await new Promise(resolve => setTimeout(resolve, 10));

    if (!audioFinished) {
      throw new Error('Audio finished callback not executed');
    }

    if (!events.includes('audio:loading')) {
      throw new Error('Audio loading state not recorded');
    }

    if (!events.includes('audio:ended')) {
      throw new Error('Audio ended state not recorded');
    }
  }

  private async testErrorRecovery(): Promise<void> {
    let errorHandled = false;

    await audioStateManager.playAudio(
      'test.mp3',
      undefined,
      undefined,
      () => { errorHandled = true; }
    );

    // Simulate audio error
    const mockAudio = audioStateManager.getState().currentAudio as any;
    mockAudio?.listeners?.error?.forEach((cb: Function) => cb({ type: 'network' }));

    await new Promise(resolve => setTimeout(resolve, 10));

    if (!errorHandled) {
      throw new Error('Error callback not executed');
    }

    if (audioStateManager.getState().state !== 'error') {
      throw new Error('Audio state not updated to error');
    }
  }

  private printResults(): void {
    console.log('\n📊 Test Results Summary:');
    console.log('========================');

    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => r.failed).length;
    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0);

    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⏱️  Total time: ${totalTime}ms`);
    console.log(`📈 Success rate: ${((passed / this.results.length) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log('\n❌ Failed tests:');
      this.results
        .filter(r => !r.passed)
        .forEach(r => console.log(`   - ${r.name}: ${r.error}`));
    }

    console.log('\n🎯 Race condition and timing fixes verification:');
    if (passed === this.results.length) {
      console.log('✅ All timing issues have been resolved!');
      console.log('✅ Race conditions have been eliminated!');
      console.log('✅ Callback execution is now reliable!');
      console.log('✅ State transitions are properly managed!');
    } else {
      console.log('⚠️  Some timing issues may still exist. Review failed tests.');
    }
  }
}

// Export for use in tests
export { TimingTestRunner };

// Run tests if this file is executed directly
if (require.main === module) {
  const runner = new TimingTestRunner();
  runner.runAllTests().catch(console.error);
}
