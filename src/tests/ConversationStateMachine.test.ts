/**
 * Tests for ConversationStateMachine
 * Verifies that state transitions are properly validated and race conditions are prevented
 */

import { ConversationStateMachine, createConversationStateMachine } from '../services/ConversationStateMachine';
import type { ConversationState } from '../services/impl/ISpeechRecognitionService';

describe('ConversationStateMachine', () => {
  let stateMachine: ConversationStateMachine;
  let stateChanges: Array<{ from: ConversationState; to: ConversationState }> = [];
  let invalidTransitions: Array<{ from: ConversationState; to: ConversationState }> = [];

  beforeEach(() => {
    stateChanges = [];
    invalidTransitions = [];
    
    stateMachine = createConversationStateMachine(
      (from, to) => stateChanges.push({ from, to }),
      (from, to) => invalidTransitions.push({ from, to })
    );
  });

  describe('Initial State', () => {
    test('should start in idle state', () => {
      expect(stateMachine.getCurrentState()).toBe('idle');
    });
  });

  describe('Valid Transitions', () => {
    test('should allow idle → listening transition', async () => {
      const success = await stateMachine.transitionTo('listening');
      
      expect(success).toBe(true);
      expect(stateMachine.getCurrentState()).toBe('listening');
      expect(stateChanges).toHaveLength(1);
      expect(stateChanges[0]).toEqual({ from: 'idle', to: 'listening' });
    });

    test('should allow idle → speaking transition', async () => {
      const success = await stateMachine.transitionTo('speaking');
      
      expect(success).toBe(true);
      expect(stateMachine.getCurrentState()).toBe('speaking');
    });

    test('should allow listening → processing transition', async () => {
      await stateMachine.transitionTo('listening');
      const success = await stateMachine.transitionTo('processing');
      
      expect(success).toBe(true);
      expect(stateMachine.getCurrentState()).toBe('processing');
    });

    test('should allow processing → speaking transition', async () => {
      await stateMachine.transitionTo('listening');
      await stateMachine.transitionTo('processing');
      const success = await stateMachine.transitionTo('speaking');
      
      expect(success).toBe(true);
      expect(stateMachine.getCurrentState()).toBe('speaking');
    });

    test('should allow speaking → idle transition', async () => {
      await stateMachine.transitionTo('speaking');
      const success = await stateMachine.transitionTo('idle');
      
      expect(success).toBe(true);
      expect(stateMachine.getCurrentState()).toBe('idle');
    });
  });

  describe('Same State Transitions', () => {
    test('should allow transition to same state', async () => {
      const success = await stateMachine.transitionTo('idle');
      
      expect(success).toBe(true);
      expect(stateMachine.getCurrentState()).toBe('idle');
      expect(stateChanges).toHaveLength(0); // No change recorded for same state
    });
  });

  describe('Race Condition Prevention', () => {
    test('should prevent concurrent transitions', async () => {
      // Start multiple transitions simultaneously
      const promises = [
        stateMachine.transitionTo('listening'),
        stateMachine.transitionTo('speaking'),
        stateMachine.transitionTo('processing')
      ];

      const results = await Promise.all(promises);
      
      // Only one should succeed
      const successCount = results.filter(result => result).length;
      expect(successCount).toBe(1);
      
      // State should be one of the target states
      const finalState = stateMachine.getCurrentState();
      expect(['listening', 'speaking', 'processing']).toContain(finalState);
    });

    test('should queue transitions properly', async () => {
      // Start first transition
      const firstTransition = stateMachine.transitionTo('listening');
      
      // Immediately start second transition (should be ignored)
      const secondTransition = stateMachine.transitionTo('speaking');
      
      const [first, second] = await Promise.all([firstTransition, secondTransition]);
      
      expect(first).toBe(true);
      expect(second).toBe(false); // Should be rejected due to ongoing transition
      expect(stateMachine.getCurrentState()).toBe('listening');
    });
  });

  describe('State Queries', () => {
    test('should return possible next states from idle', () => {
      const possibleStates = stateMachine.getPossibleNextStates();
      
      expect(possibleStates).toContain('listening');
      expect(possibleStates).toContain('speaking');
    });

    test('should return possible next states from listening', async () => {
      await stateMachine.transitionTo('listening');
      const possibleStates = stateMachine.getPossibleNextStates();
      
      expect(possibleStates).toContain('idle');
      expect(possibleStates).toContain('processing');
      expect(possibleStates).toContain('speaking');
    });

    test('should check if transition is valid', () => {
      expect(stateMachine.canTransitionTo('listening')).toBe(true);
      expect(stateMachine.canTransitionTo('speaking')).toBe(true);
    });
  });

  describe('Reset Functionality', () => {
    test('should reset to idle state', async () => {
      await stateMachine.transitionTo('listening');
      await stateMachine.transitionTo('processing');
      
      stateMachine.reset();
      
      expect(stateMachine.getCurrentState()).toBe('idle');
    });

    test('should reset to custom state', async () => {
      await stateMachine.transitionTo('listening');
      
      stateMachine.reset('speaking');
      
      expect(stateMachine.getCurrentState()).toBe('speaking');
    });
  });

  describe('Error Recovery', () => {
    test('should allow recovery from any state to idle', async () => {
      // Test from listening
      await stateMachine.transitionTo('listening');
      expect(await stateMachine.transitionTo('idle')).toBe(true);
      
      // Test from processing
      await stateMachine.transitionTo('listening');
      await stateMachine.transitionTo('processing');
      expect(await stateMachine.transitionTo('idle')).toBe(true);
      
      // Test from speaking
      await stateMachine.transitionTo('speaking');
      expect(await stateMachine.transitionTo('idle')).toBe(true);
    });
  });

  describe('Transition Actions', () => {
    test('should execute transition actions', async () => {
      let actionExecuted = false;
      
      const customStateMachine = new ConversationStateMachine({
        initialState: 'idle',
        transitions: [
          {
            from: 'idle',
            to: 'listening',
            action: async () => {
              actionExecuted = true;
            }
          }
        ]
      });

      await customStateMachine.transitionTo('listening');
      
      expect(actionExecuted).toBe(true);
    });

    test('should handle action errors gracefully', async () => {
      const customStateMachine = new ConversationStateMachine({
        initialState: 'idle',
        transitions: [
          {
            from: 'idle',
            to: 'listening',
            action: async () => {
              throw new Error('Action failed');
            }
          }
        ]
      });

      const success = await customStateMachine.transitionTo('listening');
      
      expect(success).toBe(false);
      expect(customStateMachine.getCurrentState()).toBe('idle'); // Should remain in original state
    });
  });

  describe('Conditional Transitions', () => {
    test('should respect transition conditions', async () => {
      let conditionMet = false;
      
      const customStateMachine = new ConversationStateMachine({
        initialState: 'idle',
        transitions: [
          {
            from: 'idle',
            to: 'listening',
            condition: () => conditionMet
          }
        ]
      });

      // Should fail when condition is false
      expect(await customStateMachine.transitionTo('listening')).toBe(false);
      
      // Should succeed when condition is true
      conditionMet = true;
      expect(await customStateMachine.transitionTo('listening')).toBe(true);
    });
  });

  describe('Callback Notifications', () => {
    test('should notify on state changes', async () => {
      await stateMachine.transitionTo('listening');
      await stateMachine.transitionTo('processing');
      
      expect(stateChanges).toHaveLength(2);
      expect(stateChanges[0]).toEqual({ from: 'idle', to: 'listening' });
      expect(stateChanges[1]).toEqual({ from: 'listening', to: 'processing' });
    });

    test('should notify on invalid transitions', async () => {
      // Create a restricted state machine for testing
      const restrictedStateMachine = new ConversationStateMachine({
        initialState: 'idle',
        transitions: [
          { from: 'idle', to: 'listening' }
          // No transition from idle to speaking
        ],
        onInvalidTransition: (from, to) => invalidTransitions.push({ from, to })
      });

      await restrictedStateMachine.transitionTo('speaking');
      
      expect(invalidTransitions).toHaveLength(1);
      expect(invalidTransitions[0]).toEqual({ from: 'idle', to: 'speaking' });
    });
  });
});
